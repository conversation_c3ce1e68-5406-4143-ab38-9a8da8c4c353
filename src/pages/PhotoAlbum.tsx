import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Heart, MessageSquare, Share2, Upload } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { PhotoUploadModal } from "@/components/PhotoUploadModal";
import { CommentSection } from "@/components/CommentSection";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

interface Comment {
  id: string;
  user: {
    name: string;
    avatar: string;
    initials: string;
  };
  text: string;
  timestamp: string;
}

interface PhotoPost {
  id: string;
  user: {
    name: string;
    avatar: string;
    initials: string;
  };
  tour: string;
  image: string;
  caption: string;
  likes: number;
  comments: Comment[];
  commentCount: number;
  timestamp: string;
  isLiked: boolean;
}

const PhotoAlbum = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [commentingOnPost, setCommentingOnPost] = useState<string | null>(null);

  // Fetch posts from database
  const { data: posts = [], isLoading } = useQuery({
    queryKey: ['posts'],
    queryFn: async () => {
      const { data: postsData, error: postsError } = await supabase
        .from('posts')
        .select('*')
        .order('created_at', { ascending: false });

      if (postsError) throw postsError;

      // Get user profiles and comments for each post
      const postsWithDetails = await Promise.all(
        postsData.map(async (post) => {
          // Get user profile
          const { data: profile } = await supabase
            .from('profiles')
            .select('display_name, username, avatar_url')
            .eq('user_id', post.profile_id)
            .single();

          // Get comments for this post
          const { data: comments, error: commentsError } = await supabase
            .from('comments')
            .select('*')
            .eq('post_id', post.id)
            .order('created_at', { ascending: true });

          if (commentsError) throw commentsError;

          // Get comment user profiles
          const commentsWithProfiles = await Promise.all(
            (comments || []).map(async (comment) => {
              const { data: commentProfile } = await supabase
                .from('profiles')
                .select('display_name, username, avatar_url')
                .eq('user_id', comment.profile_id)
                .single();

              return {
                id: comment.id,
                user: {
                  name: commentProfile?.display_name || commentProfile?.username || 'Anonymous',
                  avatar: commentProfile?.avatar_url || `https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face`,
                  initials: (commentProfile?.display_name || commentProfile?.username || 'A').charAt(0).toUpperCase()
                },
                text: comment.text,
                timestamp: new Date(comment.created_at).toLocaleDateString()
              };
            })
          );

          // Check if current user liked this post
          let userLike = null;
          if (user?.id) {
            const { data } = await supabase
              .from('likes')
              .select('id')
              .eq('target_id', post.id)
              .eq('target_type', 'post')
              .eq('profile_id', user.id)
              .single();
            userLike = data;
          }

          return {
            id: post.id,
            user: {
              name: profile?.display_name || profile?.username || 'Anonymous',
              avatar: profile?.avatar_url || `https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face`,
              initials: (profile?.display_name || profile?.username || 'A').charAt(0).toUpperCase()
            },
            tour: post.tour,
            image: post.image_url,
            caption: post.caption,
            likes: post.likes_count,
            comments: commentsWithProfiles,
            commentCount: post.comments_count,
            timestamp: new Date(post.created_at).toLocaleDateString(),
            isLiked: !!userLike
          };
        })
      );

      return postsWithDetails;
    },
    enabled: !!user
  });

  // Toggle like mutation
  const toggleLikeMutation = useMutation({
    mutationFn: async ({ postId, isLiked }: { postId: string; isLiked: boolean }) => {
      if (isLiked) {
        // Remove like
        const { error } = await supabase
          .from('likes')
          .delete()
          .eq('target_id', postId)
          .eq('target_type', 'post')
          .eq('profile_id', user!.id);
        if (error) throw error;
      } else {
        // Add like
        const { error } = await supabase
          .from('likes')
          .insert({
            target_id: postId,
            target_type: 'post',
            profile_id: user!.id
          });
        if (error) throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['posts'] });
    }
  });

  const toggleLike = (postId: string) => {
    if (!user) {
      toast({
        title: "Please log in",
        description: "You need to be logged in to like posts",
        variant: "destructive"
      });
      return;
    }
    
    const post = posts.find(p => p.id === postId);
    if (post) {
      toggleLikeMutation.mutate({ postId, isLiked: post.isLiked });
    }
  };

  const handleShare = (postId: string) => {
    toast({
      title: "Shared!",
      description: "Post shared successfully",
    });
  };

  // Add comment mutation
  const addCommentMutation = useMutation({
    mutationFn: async ({ postId, text }: { postId: string; text: string }) => {
      const { error } = await supabase
        .from('comments')
        .insert({
          post_id: postId,
          profile_id: user!.id,
          text
        });
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['posts'] });
    }
  });

  const handleAddComment = (postId: string, newComment: Comment) => {
    if (!user) {
      toast({
        title: "Please log in",
        description: "You need to be logged in to comment",
        variant: "destructive"
      });
      return;
    }
    
    addCommentMutation.mutate({ postId, text: newComment.text });
  };

  const handleUpload = () => {
    setIsUploadModalOpen(true);
  };

  const handlePhotoUpload = () => {
    queryClient.invalidateQueries({ queryKey: ['posts'] });
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-4">
            Travel Photo Album
          </h1>
          <p className="text-xl text-muted-foreground mb-6">
            Share your amazing Tanzania experiences with fellow travelers
          </p>
          <Button onClick={handleUpload} className="mb-6">
            <Upload className="w-4 h-4 mr-2" />
            Upload Your Photos
          </Button>
        </div>

        <div className="space-y-6">
          {isLoading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Loading posts...</p>
            </div>
          ) : posts.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No posts yet. Be the first to share your travel photos!</p>
            </div>
          ) : (
            posts.map((post) => (
            <Card key={post.id} className="overflow-hidden">
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-3">
                  <Avatar>
                    <AvatarImage src={post.user.avatar} alt={post.user.name} />
                    <AvatarFallback>{post.user.initials}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h3 className="font-semibold text-foreground">{post.user.name}</h3>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="text-xs">
                        {post.tour}
                      </Badge>
                      <span className="text-xs text-muted-foreground">{post.timestamp}</span>
                    </div>
                  </div>
                </div>
              </CardHeader>

              <div className="aspect-[4/3] overflow-hidden">
                <img
                  src={post.image}
                  alt="Travel photo"
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                />
              </div>

              <CardContent className="pt-4">
                <p className="text-foreground mb-4">{post.caption}</p>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={() => toggleLike(post.id)}
                      className="flex items-center space-x-1 hover:text-red-500 transition-colors"
                    >
                      <Heart 
                        className={`w-5 h-5 ${post.isLiked ? 'fill-red-500 text-red-500' : 'text-muted-foreground'}`} 
                      />
                      <span className="text-sm font-medium">{post.likes}</span>
                    </button>
                    
                    <div className="flex items-center space-x-1 text-muted-foreground cursor-pointer hover:text-foreground transition-colors" 
                         onClick={() => setCommentingOnPost(commentingOnPost === post.id ? null : post.id)}>
                      <MessageSquare className="w-5 h-5" />
                      <span className="text-sm">{post.commentCount}</span>
                    </div>
                  </div>
                  
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => handleShare(post.id)}
                  >
                    <Share2 className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>

              <CardFooter className="pt-0">
                <CommentSection
                  postId={post.id}
                  comments={post.comments}
                  onAddComment={handleAddComment}
                  isVisible={commentingOnPost === post.id}
                />
              </CardFooter>
            </Card>
            ))
          )}
        </div>
      </div>

      <PhotoUploadModal 
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        onUpload={handlePhotoUpload}
      />
    </div>
  );
};

export default PhotoAlbum;