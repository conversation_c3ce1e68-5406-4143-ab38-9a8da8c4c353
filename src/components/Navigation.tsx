import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import { Menu, Book, ShoppingCart, Album, Star, User, LogIn } from "lucide-react";
import { useCart } from "@/contexts/CartContext";
import { useAuth } from "@/contexts/AuthContext";
import { CartModal } from "./CartModal";

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isCartModalOpen, setIsCartModalOpen] = useState(false);
  const location = useLocation();
  const { getTotalItems } = useCart();
  const { user, logout } = useAuth();

  const navigationItems = [
    { name: "Home", path: "/", icon: null },
    { name: "Tours", path: "/tours", icon: Book },
    { name: "Shop", path: "/shop", icon: ShoppingCart },
    { name: "Photo Album", path: "/album", icon: Album },
    { name: "Reviews", path: "/reviews", icon: Star }
  ];

  const isActive = (path: string) => location.pathname === path;

  const handleAuthAction = () => {
    if (user) {
      logout();
    } else {
      window.location.href = '/auth';
    }
  };

  return (
    <nav className="sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">TZ</span>
            </div>
            <span className="font-bold text-xl text-foreground">Tanzania Tours</span>
          </Link>

          <div className="hidden md:flex items-center space-x-6">
            {navigationItems.map((item) => (
              <Link
                key={item.name}
                to={item.path}
                className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${
                  isActive(item.path)
                    ? "bg-primary text-primary-foreground"
                    : "text-muted-foreground hover:text-foreground hover:bg-muted"
                }`}
              >
                {item.icon && <item.icon className="w-4 h-4" />}
                <span>{item.name}</span>
              </Link>
            ))}
            
            <div className="relative">
              <Button variant="ghost" size="icon" onClick={() => setIsCartModalOpen(true)}>
                <ShoppingCart className="w-5 h-5" />
                {getTotalItems() > 0 && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center text-xs p-0"
                  >
                    {getTotalItems()}
                  </Badge>
                )}
              </Button>
            </div>
            
            <Button variant="ghost" onClick={handleAuthAction} className="flex items-center space-x-2">
              {user ? (
                <>
                  <User className="w-4 h-4" />
                  <span>{user.profile?.display_name || user.email}</span>
                </>
              ) : (
                <>
                  <LogIn className="w-4 h-4" />
                  <span>Login</span>
                </>
              )}
            </Button>
          </div>

          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild className="md:hidden">
              <Button variant="ghost" size="icon">
                <Menu className="w-5 h-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-64">
              <div className="flex flex-col space-y-4 mt-6">
                <div className="flex items-center space-x-2 mb-6">
                  <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                    <span className="text-primary-foreground font-bold text-sm">TZ</span>
                  </div>
                  <span className="font-bold text-lg text-foreground">Tanzania Tours</span>
                </div>
                
                {navigationItems.map((item) => (
                  <Link
                    key={item.name}
                    to={item.path}
                    onClick={() => setIsOpen(false)}
                    className={`flex items-center space-x-3 px-3 py-3 rounded-md transition-colors ${
                      isActive(item.path)
                        ? "bg-primary text-primary-foreground"
                        : "text-muted-foreground hover:text-foreground hover:bg-muted"
                    }`}
                  >
                    {item.icon && <item.icon className="w-5 h-5" />}
                    <span className="font-medium">{item.name}</span>
                  </Link>
                ))}
                  
                <div className="border-t pt-4 mt-4 space-y-3">
                  <Button
                    variant="ghost"
                    onClick={() => {
                      setIsCartModalOpen(true);
                      setIsOpen(false);
                    }}
                    className="w-full justify-start space-x-3 px-3 py-3 h-auto"
                  >
                    <div className="relative">
                      <ShoppingCart className="w-5 h-5" />
                      {getTotalItems() > 0 && (
                        <Badge 
                          variant="destructive" 
                          className="absolute -top-1 -right-1 h-4 w-4 flex items-center justify-center text-xs p-0"
                        >
                          {getTotalItems()}
                        </Badge>
                      )}
                    </div>
                    <span className="font-medium">Cart</span>
                  </Button>
                  
                  <Button 
                    variant="ghost" 
                    onClick={() => {
                      handleAuthAction();
                      setIsOpen(false);
                    }}
                    className="w-full justify-start space-x-3 px-3 py-3 h-auto"
                  >
                    {user ? <User className="w-5 h-5" /> : <LogIn className="w-5 h-5" />}
                    <span className="font-medium">{user ? (user.profile?.display_name || user.email) : "Login"}</span>
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
      
      <CartModal isOpen={isCartModalOpen} onClose={() => setIsCartModalOpen(false)} />
    </nav>
  );
};

export default Navigation;