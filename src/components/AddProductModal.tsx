import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alog<PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";

interface AddProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  onProductAdded: () => void;
}

export const AddProductModal = ({ isOpen, onClose, onProductAdded }: AddProductModalProps) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [productData, setProductData] = useState({
    name: "",
    description: "",
    price: "",
    price_type: "fixed",
    price_min: "",
    price_max: "",
    category: "",
    image_url: "",
    in_stock: true,
    discount_percentage: 0,
    promotion_text: "",
    promotion_expires_at: ""
  });
  const [isLoading, setIsLoading] = useState(false);

  const categories = [
    "Jewelry",
    "Food & Beverage", 
    "Apparel",
    "Textiles",
    "Art & Crafts",
    "Souvenirs",
    "Electronics",
    "Books",
    "Home & Garden",
    "Sports & Outdoors"
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      navigate('/auth');
      return;
    }

    // Validation based on price type
    const isPriceValid = productData.price_type === 'fixed' 
      ? productData.price 
      : productData.price_min && productData.price_max;
    
    if (!productData.name.trim() || !productData.description.trim() || !isPriceValid || !productData.category || !productData.image_url.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    try {
      const { error } = await supabase
        .from('products')
        .insert({
          seller_id: user.id,
          name: productData.name.trim(),
          description: productData.description.trim(),
          price: productData.price_type === 'fixed' ? parseFloat(productData.price) : parseFloat(productData.price_min),
          price_type: productData.price_type,
          price_min: productData.price_type === 'fixed' ? parseFloat(productData.price) : parseFloat(productData.price_min),
          price_max: productData.price_type === 'range' ? parseFloat(productData.price_max) : null,
          category: productData.category,
          image_url: productData.image_url.trim(),
          in_stock: productData.in_stock,
          discount_percentage: parseInt(productData.discount_percentage.toString()) || 0,
          promotion_text: productData.promotion_text.trim() || null,
          promotion_expires_at: productData.promotion_expires_at ? new Date(productData.promotion_expires_at).toISOString() : null
        });

      if (error) throw error;

      setProductData({
        name: "",
        description: "",
        price: "",
        price_type: "fixed",
        price_min: "",
        price_max: "",
        category: "",
        image_url: "",
        in_stock: true,
        discount_percentage: 0,
        promotion_text: "",
        promotion_expires_at: ""
      });

      onProductAdded();
    } catch (error) {
      console.error('Error adding product:', error);
      toast({
        title: "Error",
        description: "Failed to add product. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add New Product</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2">
              Basic Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="name">Product Name *</Label>
                <Input
                  id="name"
                  value={productData.name}
                  onChange={(e) => setProductData({ ...productData, name: e.target.value })}
                  placeholder="Enter product name"
                  required
                />
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={productData.description}
                  onChange={(e) => setProductData({ ...productData, description: e.target.value })}
                  placeholder="Describe your product"
                  rows={3}
                  required
                />
              </div>

              <div>
                <Label htmlFor="category">Category *</Label>
                <Select value={productData.category} onValueChange={(value) => setProductData({ ...productData, category: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="image_url">Image URL *</Label>
                <Input
                  id="image_url"
                  type="url"
                  value={productData.image_url}
                  onChange={(e) => setProductData({ ...productData, image_url: e.target.value })}
                  placeholder="https://example.com/image.jpg"
                  required
                />
              </div>
            </div>
          </div>

          {/* Pricing */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2">
              Pricing
            </h3>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="price_type">Price Type *</Label>
                <Select value={productData.price_type} onValueChange={(value) => setProductData({ ...productData, price_type: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select price type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="fixed">Fixed Price</SelectItem>
                    <SelectItem value="range">Price Range</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {productData.price_type === 'fixed' ? (
                <div>
                  <Label htmlFor="price">Price ($) *</Label>
                  <Input
                    id="price"
                    type="number"
                    step="0.01"
                    min="0"
                    value={productData.price}
                    onChange={(e) => setProductData({ ...productData, price: e.target.value })}
                    placeholder="0.00"
                    required
                  />
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="price_min">Min Price ($) *</Label>
                    <Input
                      id="price_min"
                      type="number"
                      step="0.01"
                      min="0"
                      value={productData.price_min}
                      onChange={(e) => setProductData({ ...productData, price_min: e.target.value })}
                      placeholder="0.00"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="price_max">Max Price ($) *</Label>
                    <Input
                      id="price_max"
                      type="number"
                      step="0.01"
                      min="0"
                      value={productData.price_max}
                      onChange={(e) => setProductData({ ...productData, price_max: e.target.value })}
                      placeholder="0.00"
                      required
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Promotions & Stock */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2">
              Promotions & Stock
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="discount_percentage">Discount (%)</Label>
                <Input
                  id="discount_percentage"
                  type="number"
                  min="0"
                  max="100"
                  value={productData.discount_percentage}
                  onChange={(e) => setProductData({ ...productData, discount_percentage: parseInt(e.target.value) || 0 })}
                  placeholder="0"
                />
              </div>

              <div className="flex items-center space-x-3 pt-6">
                <Switch
                  id="in_stock"
                  checked={productData.in_stock}
                  onCheckedChange={(checked) => setProductData({ ...productData, in_stock: checked })}
                />
                <Label htmlFor="in_stock" className="text-sm font-medium">
                  In Stock
                </Label>
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="promotion_text">Promotion Text</Label>
                <Input
                  id="promotion_text"
                  value={productData.promotion_text}
                  onChange={(e) => setProductData({ ...productData, promotion_text: e.target.value })}
                  placeholder="e.g., Limited time offer!"
                />
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="promotion_expires_at">Promotion Expires</Label>
                <Input
                  id="promotion_expires_at"
                  type="datetime-local"
                  value={productData.promotion_expires_at}
                  onChange={(e) => setProductData({ ...productData, promotion_expires_at: e.target.value })}
                />
              </div>
            </div>
          </div>

          <div className="flex gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading} className="flex-1">
              {isLoading ? "Adding..." : "Add Product"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};