-- Add columns for price ranges, discounts, and promotions to products table
ALTER TABLE public.products 
ADD COLUMN price_type TEXT DEFAULT 'fixed' CHECK (price_type IN ('fixed', 'range')),
ADD COLUMN price_min DECIMAL(10,2),
ADD COLUMN price_max DECIMAL(10,2),
ADD COLUMN discount_percentage INTEGER DEFAULT 0 CHECK (discount_percentage >= 0 AND discount_percentage <= 100),
ADD COLUMN promotion_text TEXT,
ADD COLUMN promotion_expires_at TIMESTAMP WITH TIME ZONE;

-- Update existing products to have price_min set to their current price
UPDATE public.products SET price_min = price WHERE price_type = 'fixed';

-- Add index for better performance on price queries
CREATE INDEX idx_products_price_range ON public.products(price_min, price_max);
CREATE INDEX idx_products_promotion_expires ON public.products(promotion_expires_at) WHERE promotion_expires_at IS NOT NULL;