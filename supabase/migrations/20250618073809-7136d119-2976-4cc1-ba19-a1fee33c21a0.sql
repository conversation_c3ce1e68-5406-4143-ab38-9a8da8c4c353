-- Drop existing foreign key constraints
ALTER TABLE public.posts DROP CONSTRAINT IF EXISTS posts_user_id_fkey;
ALTER TABLE public.comments DROP CONSTRAINT IF EXISTS comments_user_id_fkey;
ALTER TABLE public.reviews DROP CONSTRAINT IF EXISTS reviews_user_id_fkey;
ALTER TABLE public.likes DROP CONSTRAINT IF EXISTS likes_user_id_fkey;

-- Add correct foreign key constraints pointing to profiles.id (not profiles.user_id)
-- We need to first create a mapping since posts.user_id should actually reference auth.users.id
-- Let's change the approach: rename user_id columns to profile_id and reference profiles.id

-- For posts table
ALTER TABLE public.posts RENAME COLUMN user_id TO profile_id;
ALTER TABLE public.posts 
ADD CONSTRAINT posts_profile_id_fkey 
FOREIGN KEY (profile_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- For comments table  
ALTER TABLE public.comments RENAME COLUMN user_id TO profile_id;
ALTER TABLE public.comments 
ADD CONSTRAINT comments_profile_id_fkey 
FOREIGN KEY (profile_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- For reviews table
ALTER TABLE public.reviews RENAME COLUMN user_id TO profile_id;
ALTER TABLE public.reviews 
ADD CONSTRAINT reviews_profile_id_fkey 
FOREIGN KEY (profile_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- For likes table
ALTER TABLE public.likes RENAME COLUMN user_id TO profile_id;
ALTER TABLE public.likes 
ADD CONSTRAINT likes_profile_id_fkey 
FOREIGN KEY (profile_id) REFERENCES public.profiles(id) ON DELETE CASCADE;